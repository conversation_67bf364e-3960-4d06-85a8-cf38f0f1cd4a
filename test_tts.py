#!/usr/bin/env python3
"""
Test script to verify TTS providers work correctly
"""
import os
import sys

# Add current directory to path to import from app.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import generate_tts, TTS_PROVIDERS

def test_tts_provider(provider_name, test_text="Hello, this is a test of the text to speech system."):
    """Test a specific TTS provider"""
    print(f"\n=== Testing {provider_name} TTS ===")
    
    if provider_name not in TTS_PROVIDERS:
        print(f"❌ Provider {provider_name} not found in TTS_PROVIDERS")
        return False
    
    try:
        # Create audio directory if it doesn't exist
        os.makedirs('audio', exist_ok=True)
        
        # Test the provider
        result = generate_tts(
            text=test_text,
            provider=provider_name,
            original_filename=f"test_{provider_name}"
        )
        
        if result:
            print(f"✅ {provider_name} TTS generated successfully: {result}")
            
            # Check if file exists
            audio_path = os.path.join('audio', result)
            if os.path.exists(audio_path):
                file_size = os.path.getsize(audio_path)
                print(f"   File size: {file_size} bytes")
                return True
            else:
                print(f"❌ Audio file not found: {audio_path}")
                return False
        else:
            print(f"❌ {provider_name} TTS failed to generate audio")
            return False
            
    except Exception as e:
        print(f"❌ {provider_name} TTS failed with error: {e}")
        return False

def main():
    """Test all available TTS providers"""
    print("🎤 Testing TTS Providers")
    print("=" * 50)
    
    test_text = "Hello! This is a test of the text to speech system. How does it sound?"
    
    results = {}
    
    # Test each provider
    for provider_name in TTS_PROVIDERS.keys():
        results[provider_name] = test_tts_provider(provider_name, test_text)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    working_providers = []
    failed_providers = []
    
    for provider, success in results.items():
        status = "✅ WORKING" if success else "❌ FAILED"
        provider_info = TTS_PROVIDERS[provider]
        free_status = "FREE" if provider_info.get("free", False) else "PAID"
        unlimited_status = "UNLIMITED" if provider_info.get("unlimited", True) else "LIMITED"
        
        print(f"{provider:12} | {status:12} | {free_status:8} | {unlimited_status}")
        
        if success:
            working_providers.append(provider)
        else:
            failed_providers.append(provider)
    
    print("\n🎯 RECOMMENDATIONS:")
    if working_providers:
        print(f"✅ Working providers: {', '.join(working_providers)}")
        
        # Find best free provider
        free_working = [p for p in working_providers if TTS_PROVIDERS[p].get("free", False)]
        if free_working:
            print(f"💰 Free working providers: {', '.join(free_working)}")
            
            unlimited_free = [p for p in free_working if TTS_PROVIDERS[p].get("unlimited", True)]
            if unlimited_free:
                print(f"🚀 Best choice (free + unlimited): {unlimited_free[0]}")
    
    if failed_providers:
        print(f"❌ Failed providers: {', '.join(failed_providers)}")
    
    print(f"\n📁 Audio files saved in: {os.path.abspath('audio')}")

if __name__ == "__main__":
    main()
